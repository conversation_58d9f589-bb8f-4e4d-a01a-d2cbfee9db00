package main

import (
	"context"
	"encoding/json"
	"fmt"

	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/knadh/listmonk/external"
	"github.com/knadh/listmonk/logger"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/tracer"
	"github.com/knadh/listmonk/utils"
	"github.com/nats-io/nats.go"
	push "github.com/phuslu/log"
)

var (
	jetStreamContext            nats.JetStreamContext
	listmonkSubscription        *nats.Subscription
	listMonkSubject             string
	listMonkMemberSubject       string
	natConnection               *nats.Conn
	segmentationSubscription    *nats.Subscription
	reportCompletedSubscription *nats.Subscription
	broadcastSubject            string
	webhookSubject              string

	txnEventSubject            string
	broadcastSubscription      *nats.Subscription
	webhookSubscription        *nats.Subscription
	listmonkMemberSubscription *nats.Subscription
	txnEventSubscription       *nats.Subscription
	app                        *App
)

func initNats(ap *App) {
	app = ap
	nc, err := nats.Connect(config.ServerConfig.NatsUrl)

	if err != nil {
		panic(err)
	} else {
		natConnection = nc
	}

	initListMonkStream()

	//initSegmentationStream()

}

func GetListmonkCronSubject() string {
	return listMonkSubject
}

func GetListmonkMemberSubject() string {
	return listMonkMemberSubject
}

func GetBroadcastSubject() string {
	return broadcastSubject
}

func initListMonkStream() {

	stream := "listmonkstream"
	//use wildcard instead of explicit
	subject := "listmonk.campaigns.update"
	membershipSubject := "listmonk.campaigns.members"
	txnEventSubject = "listmonk.notification"
	broadcastSubject = "listmonk.campaigns.broadcast1"
	webhookSubject = "listmonk.webhook.status"
	listMonkSubject = subject
	listMonkMemberSubject = membershipSubject
	wildcardProducerSubject := "listmonk.>"
	// wildcardProducerSubject := []string{subject, membershipSubject, broadcastSubject}
	replicaLevel := config.ServerConfig.NatsReplica

	StreamInfo := addOrUpdateStream(natConnection, stream, wildcardProducerSubject, replicaLevel)
	jsonData, err := json.Marshal(StreamInfo)

	if err != nil {
		panic(err)
	}

	lo.Info().Msgf("successfully initialized listmonk stream with info: %s", string(jsonData))

	listmonkSubscription = registerListmonkConsumer(subject, "listmonkdurable")

	listmonkMemberSubscription = registerListmonkConsumer(membershipSubject, "listmonkdurable")

	txnEventSubscription = registerListmonkConsumer(txnEventSubject, "listmonkdurable")

	broadcastSubscription = registerListmonkConsumer(broadcastSubject, "")

	webhookSubscription = registerListmonkConsumer(webhookSubject, "listmonkdurable")
	app.manager.SetWebhookSubject(webhookSubject)

	updateConsumerAckWait(stream)

	go startListmonkConsumer()
	go startListmonkMemberConsumer()
	go startListmonkTxnEventConsumer()
	go startBroadcastConsumer()
	go startWebhookConsumer()

}

// Update AckWait for an existing consumer
func updateConsumerAckWait(streamName string) {
	consumerChan := jetStreamContext.Consumers(streamName)

	for consumer := range consumerChan {
		consumerName := consumer.Name
		lo.Info().Msgf("consumer name is: %s", consumerName)
		if consumerName == "" {
			lo.Info().Msgf("Skipping consumer with empty name %s", consumerName)
			continue
		}
		consumerInfo, err := jetStreamContext.ConsumerInfo(streamName, consumerName)
		if err != nil {
			lo.Fatal().Msgf("Failed to get consumer info for %s: %v", consumerName, err)
		}
		consumerConfig := consumerInfo.Config
		if consumerConfig.AckWait == 2*time.Minute {
			lo.Info().Msgf("AckWait is already set to 2 minutes for consumer %s", consumerName)
			continue
		}
		consumerConfig.AckWait = 2 * time.Minute
		_, err = jetStreamContext.UpdateConsumer(streamName, &consumerConfig)
		if err != nil {
			lo.Fatal().Msgf("Failed to update AckWait for %s: %v", consumerName, err)
		} else {
			lo.Info().Msgf("Successfully updated AckWait to 2 minutes for consumer %s", consumerName)
		}
	}
}

func registerListmonkConsumer(subject string, durable string) *nats.Subscription {

	modifiedSubject := removeSpecialChars(subject)

	if durable != "" {
		durableName := durable + modifiedSubject
		lo.Info().Msgf("durable consumer: %s", durableName)

		sub, err := jetStreamContext.PullSubscribe(subject, durableName)

		if err != nil {
			panic(err)
		}

		return sub
	} else {
		sub, err := jetStreamContext.PullSubscribe(subject, "", nats.DeliverNew())

		if err != nil {
			panic(err)
		}

		return sub

	}
}

func removeSpecialChars(input string) string {
	reg := regexp.MustCompile("[^a-zA-Z0-9]+")
	return reg.ReplaceAllString(input, "")
}

func addOrUpdateStream(con *nats.Conn, stream string, subject string, replicaLevel int) *nats.StreamInfo {

	js, er := con.JetStream()

	if er != nil {
		panic(er)
	}

	maxAge, err := parseDuration(config.ServerConfig.NatsMaxAge)

	if err != nil {
		panic(err)
	}

	duplicates, err := parseDuration(config.ServerConfig.NatsDuplicateWindow)

	if err != nil {
		panic(err)
	}

	sc := &nats.StreamConfig{
		Name:              stream,
		Subjects:          []string{subject},
		Replicas:          replicaLevel,
		Storage:           nats.FileStorage,
		MaxAge:            maxAge,
		MaxBytes:          config.ServerConfig.NatsMaxBytes,
		MaxMsgs:           config.ServerConfig.NatsMaxMessages,
		MaxMsgSize:        config.ServerConfig.NatsMaxMessageSize,
		Duplicates:        duplicates,
		MaxMsgsPerSubject: config.ServerConfig.NatsMaxMessagesPerSubject,
	}

	streamInfo, err := js.StreamInfo(stream)

	if err != nil {

		si, err2 := js.AddStream(sc)
		if err2 != nil {
			lo.Error().Msgf("Stream creation failed with error: %s", err2)
			panic(err2)
		}
		jetStreamContext = js
		return si

	} else if config.ServerConfig.NatsStreamUpdateRequired {
		lo.Info().Msg("Updating nats stream configuration")
		si, err2 := js.UpdateStream(sc)
		if err2 != nil {
			lo.Error().Msgf("Stream update failed with error: %s", err2)
			panic(err2)
		}
		jetStreamContext = js
		return si

	}

	return streamInfo
}

func initSegmentationStream() {

	subject := "segmentation.segement"

	registerSegmentationConsumer(subject, "segmentationdurable")

	go startSegmentationConsumer()

	// reportCompletionSubject := "segmentation.reporting.completed"
	reportCompletionSubject := "segmentation.reporting.campaign.completed"

	registerReportCompletedConsumer(reportCompletionSubject, "segmentationdurable")

	go startReportCompletedConsumer()

}

func registerReportCompletedConsumer(subject string, durable string) {

	modifiedSubject := removeSpecialChars(subject)
	durableName := "listmonk" + durable + modifiedSubject
	lo.Info().Msgf("durable consumer: %s", durableName)

	sub, err := jetStreamContext.PullSubscribe(subject, durableName)

	if err != nil {
		panic(err)
	}

	reportCompletedSubscription = sub

}

func registerSegmentationConsumer(subject string, durable string) {

	modifiedSubject := removeSpecialChars(subject)
	durableName := "listmonk" + durable + modifiedSubject
	lo.Info().Msgf("durable consumer: %s", durableName)

	sub, err := jetStreamContext.PullSubscribe(subject, durableName)

	if err != nil {
		panic(err)
	}

	segmentationSubscription = sub

}

func PublishToNats(data []byte, subject string, msgId string, ctx context.Context) {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	headers := make(nats.Header)
	if msgId != "" {
		logger.Context = push.NewContext(logger.Context).Str("campaignMsgId", msgId).Value()
		headers.Set("Campaign-Msg-Id", msgId)
	}
	logger.Info().Msgf("publishing message to nats: %s", string(data))
	span, _ := tracer.ZipkinTracer.StartSpanFromContext(ctx, "spanContext")

	traceID := fmt.Sprintf("%v", span.Context().TraceID.Low)
	traceIdHigh := fmt.Sprintf("%v", span.Context().TraceID.High)

	headers.Set("TraceId", traceID)
	headers.Set("TraceId-High", traceIdHigh)

	_, err := jetStreamContext.PublishMsg(&nats.Msg{
		Subject: subject,
		Data:    data,
		Header:  headers,
	})

	if err == nil {
		logger.Info().Msg("Successfully published event to nats")
	} else {
		logger.Error().Msgf("Unable to publish the event to nats %v", err)
	}
}

func startListmonkConsumer() {

	lo.Info().Msg("Started listmonk consumer")

	for {

		msgs, _ := listmonkSubscription.Fetch(1)

		for _, msg := range msgs {
			msg.Ack()
			ctx := tracer.GetNatsTracingContext(msg.Header)
			natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
			natsLogger.Info().Msgf("Broadcast Consumer = %s recieved messsage from nats: %s", broadcastSubject, string(msg.Data))
			campaignPayload := new(models.NatsCampaignPayload)

			er := json.Unmarshal(msg.Data, campaignPayload)

			if er != nil {
				natsLogger.Info().Msgf("error occured while converting the event json %v", er)
				campaignPayload = nil
				break
			}

			campaignId, err := strconv.Atoi(campaignPayload.CampaignId)

			if campaignPayload.EventType == models.RunningCampaignCron {
				processCampaign(campaignId, ctx)
				continue
			}

			if err == nil {

				var reportRequestId string
				var err2 error

				if campaignPayload.ReportName.Valid && campaignPayload.ReportName.String != "" {
					reportRequestId, err2 = external.CreateReport(campaignPayload.ReportName.String, campaignPayload.CampaignId, campaignPayload.EventTime, ctx)
					if err2 != nil {
						continue
					}
				}

				CronCampaignClone(campaignId, reportRequestId, logger.Log)

			} else {
				natsLogger.Info().Msgf("error occurred while fetching the campaign id %v", err)
			}
			campaignPayload = nil
		}

	}

}

func startSegmentationConsumer() {

	lo.Info().Msg("Started segmentation consumer")

	for {

		msgs, _ := segmentationSubscription.Fetch(1)

		for _, msg := range msgs {

			var payload models.NatsPayload
			msg.Ack()

			ctx := tracer.GetNatsTracingContext(msg.Header)
			natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
			natsLogger.Info().Msgf("Recieved message = %s", string(msg.Data))

			err := json.Unmarshal(msg.Data, &payload)
			if err != nil {
				natsLogger.Error().Msgf("error encountered while Unmarshalling segmentation update payload: %s", err)
				continue
			}

			if payload.EventData == "" {
				natsLogger.Info().Msg("no segmentation data is found")
				continue
			}

			var event models.EventData

			er := json.Unmarshal([]byte(payload.EventData), &event)

			if er != nil {
				natsLogger.Error().Msgf("error encountered while Unmarshalling segmentation update payload: %s", er)
				continue
			}

			if event.SegmentName != "" {
				UpdateListMembershipV2(event, ctx)
			}

		}

	}

}

func startReportCompletedConsumer() {

	lo.Info().Msg("Started report completed consumer")

	for {

		msgs, _ := reportCompletedSubscription.Fetch(1)

		for _, msg := range msgs {

			var payload models.NatsPayload
			msg.Ack()
			ctx := tracer.GetNatsTracingContext(msg.Header)
			natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

			natsLogger.Info().Msgf("Recieved message = %s", string(msg.Data))
			err := json.Unmarshal(msg.Data, &payload)

			if err != nil {
				natsLogger.Error().Msgf("error encountered while Unmarshalling report completion payload: %s", err)
				continue
			}

			if payload.EventData == "" {
				natsLogger.Info().Msgf("no report data is found")
				continue
			}

			var event models.NatsReportEventData

			er := json.Unmarshal([]byte(payload.EventData), &event)

			if er != nil {
				natsLogger.Error().Msgf("error encountered while Unmarshalling report completion payload: %s", er)
				continue
			}

			if !event.IsCampaignReport.Bool {
				natsLogger.Info().Msgf("Campaign report flag is %v ignoring the report completed event %v", event.IsCampaignReport, event.RequestId)
				continue
			}

			if event.RequestId.Valid {

				// Fetch campaign matching the request id
				UpdateFilePathForReportRequestId(event.RequestId.String, event.FileUrl, ctx)
			}

		}

	}

}

func addSegmentOperation(response models.SegmentMembershipResponse, listId int64, ctx context.Context) {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	subscriberIds, err := insertNewSubscriberGetIds(response, logger)

	if len(subscriberIds) == 0 {
		logger.Error().Msgf("error inserting new subscribers for segmentId %v, detailed error %v", response.Id, err)
		return
	}
	subscriberLists := createSubscriberListEntity(subscriberIds, listId)
	logger.Info().Msgf("inserting %d subscribers for list %v segmentId %v", len(subscriberIds), listId, response.Id)

	size, err := insertSubscriberLists(subscriberLists, logger)

	if err != nil {
		logger.Error().Msgf("error inserting new subscribers for segmentId %v, listId %v, detailed error %v", response.Id, listId, err)
		return
	}
	logger.Info().Msgf("inserted %d subscribers for list %v segmentId %v", size, listId, response.Id)
}

func removeSegmentOperation(response models.SegmentMembershipResponse, listId int64, app *App, ctx context.Context) {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	subscriberIds, err := insertNewSubscriberGetIds(response, logger)

	if len(subscriberIds) == 0 {
		logger.Error().Msgf("error inserting new subscribers for segmentId %v, detailed error %v", response.Id, err)
		return
	}

	logger.Info().Msgf("inserting %d subscribers for list %v segmentId %v", len(subscriberIds), listId, response.Id)

	err = app.core.DeleteSubscriptionsV2(subscriberIds, []int64{listId}, logger)

	if err != nil {
		logger.Error().Msgf("error deleting subscribers for segmentId %v, listId %v, detailed error %v", response.Id, listId, err)
		return
	}
	logger.Info().Msgf("Successfully deleted %d subscribers for list %v segmentId %v", len(subscriberIds), listId, response.Id)
}

func processCampaign(campaignId int, ctx context.Context) {

	natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	m := GetManager()
	_, has := managerPointer.FetchCampaign(campaignId)

	if !has {
		id := strconv.Itoa(campaignId)
		MoveCampaignToFailed(id)
		natsLogger.Info().Msgf("RunningCampaign: campaign with id %v not found ", campaignId)
		return
	}

	campaign, err := managerPointer.GetCampaignFromId(campaignId)

	if err != nil {
		id := strconv.Itoa(campaignId)
		MoveCampaignToFailed(id)
		natsLogger.Error().Msgf("RunningCampaign: error occured while proccessing campaingn id %v error:  %v", campaignId, err)
		return
	}

	m.AddCampaignMap(campaign)
	utils.InitializeCounter(strconv.Itoa(campaignId)+"_"+"listmonk"+"_"+"counter_perminute", 60)
	utils.InitializeCounter(strconv.Itoa(campaignId)+"_"+"listmonk"+"_"+"counter_total", 60000)
	utils.InitializeCounter(strconv.Itoa(campaignId)+"_"+"listmonk"+"_"+"success_deliveries", 60000)
	utils.InitializeCounter(strconv.Itoa(campaignId)+"_"+"listmonk"+"_"+"failure_deliveries", 60000)
	utils.InitializeCounter(strconv.Itoa(campaignId)+"_"+"listmonk"+"_"+"rejected_deliveries_due_to_duplication", 60000)
	utils.InitializeCounter(strconv.Itoa(campaignId)+"_"+"listmonk"+"_"+"rejected_deliveries_due_to_frequency_capping", 60000)
	utils.InitializeCounter(strconv.Itoa(campaignId)+"_"+"listmonk"+"_"+"rejected_deliveries_due_to_opt_out", 60000)

	batchSize := m.GetBatchSize()
	campaignStart := time.Now()
	if !campaign.FilePath.Valid || campaign.FilePath.String == "" {
		for {
			toSend := int64(campaign.ToSend)
			start := time.Now()
			natsLogger.Info().Msgf("FETCH subscriber for campaign Id %v, %v", campaignId, start)
			subs, err := m.FetchSubscriber(campaignId, batchSize, campaign.Category.Int, campaign.Messenger, campaign.DefaultPref)
			natsLogger.Info().Msgf("End subscriber for campaign Id %v,  %v", campaignId, time.Since(start).Milliseconds())

			if err != nil {
				id := strconv.Itoa(campaignId)
				MoveCampaignToFailed(id)
				natsLogger.Error().Msgf("error encountered while fetching subscribers for campaign id %d in listmonk consumer, detailed error %v", campaignId, err)
				break
			}

			if len(subs) == 0 {
				newC, err := m.ExhaustCampaignV2(campaign, "", ctx)
				if err != nil {
					natsLogger.Error().Msgf("error exhausting campaign (%s): %v", campaign.Name, err)
					break
				}
				m.SendNotif(newC, newC.Status, "", ctx)

				break
			}
			var droppedCount int64
			//this logic is equivalent to nextSubscribers in manager.go
			var natsSubscriber models.NatsMemberPayload
			for _, s := range subs {

				if !s.Preference {
					droppedCount++
					log := models.MessengerLog{

						RequestType:   "campaign",
						MessengerType: campaign.MessageType,
						TemplateId:    campaign.TemplateID,
						MemberName:    s.Name,
						MemberType:    strings.ToLower(s.Type),
						Target:        "",
						Status:        "rejected",
						Remarks:       "preferred_out",
						ReferenceID:   strconv.Itoa(campaignId),
						CreatedBy:     "system",
					}
					m.SendMessengerLogs(log)
					continue
				}

				natsSubscriber.Subscriber = s
				natsSubscriber.CampaignId = strconv.Itoa(campaign.ID)
				natsSubscriber.EventId = uuid.NewString()
				natsSubscriber.EventTime = time.Now().String()
				natsSubscriber.EventType = models.RunningCampaignCron

				jsonData, err := json.Marshal(natsSubscriber)
				if err != nil {
					natsLogger.Error().Msgf("error marshalling json in listmonk consumer with campaign id (%d) detailed error: %v", campaignId, err)
					continue
				}

				PublishToNats([]byte(jsonData), listMonkMemberSubject, fmt.Sprintf("%s_%s_%s", natsSubscriber.CampaignId, s.Name, s.Type), ctx)
			}
			if droppedCount > 0 {
				campaignKey := strconv.Itoa(campaignId) + "_listmonk_"

				totaldroppedCount := utils.UpdateCounter(campaignKey+models.RejectedDueToOptOut, droppedCount)
				total := utils.UpdateCounter(campaignKey+"counter_total", droppedCount)
				if total >= toSend {
					m.HaltCampaign("completed", campaign.ID)
					m.RemoveCampaignMap(campaign.ID)
					m.BroadcastMessage(campaign.ID, "completed", ctx)

					successCount := utils.UpdateCounter(campaignKey+models.SuccessDelivery, 0)
					failedCount := utils.UpdateCounter(campaignKey+models.FailedDelivery, 0)
					freqCapRejectedCount := utils.UpdateCounter(campaignKey+models.RejectedDueToFrequencyCapping, 0)
					dupRejectedCount := utils.UpdateCounter(campaignKey+models.RejectedDueToDuplication, 0)

					deliveryStats := models.DeliveryStatsFields{
						SuccessDelivery:               successCount,
						RejectedDueToDuplication:      dupRejectedCount,
						RejectedDueToFrequencyCapping: freqCapRejectedCount,
						RejectedDueToOptOut:           totaldroppedCount,
						FailedDelivery:                failedCount,
					}
					dsJSONBytes, err := json.Marshal(deliveryStats)
					if err != nil {
						natsLogger.Error().Msgf("Error marshalling deliveryStats for campaignID %d: %v", campaignId, err)
						return
					}

					m.UpdateCampaignDelivered(natsLogger, campaign.ID, successCount, dsJSONBytes)
					// m.store.DeleteTargets(msg.Campaign.ID)
				}
			}
		}
	} else {
		var localFile string
		if strings.HasPrefix(campaign.FilePath.String, "http") {

			localFile = strconv.Itoa(campaign.ID) + "_" + uuid.NewString()
			if strings.Contains(campaign.FilePath.String, "xlsx") {
				localFile = localFile + ".xlsx"
			}
			err := external.DownloadPreSignedUrlFile(campaign.FilePath.String, localFile, ctx)
			if err != nil {
				id := strconv.Itoa(campaignId)
				MoveCampaignToFailed(id)
				natsLogger.Error().Msgf("Error downloading presigned url file for campaign %v %v ", campaign.ID, err)
				return
			}
		} else {
			localFile = external.DownloadFromS3Bucket(config.ServerConfig.BucketName, campaign.FilePath.String,
				strconv.Itoa(campaign.ID), config.ServerConfig.AwsAccessKey, config.ServerConfig.AwsSecretKey,
				config.ServerConfig.Region, ctx)
		}
		var fileRows map[string]map[string]string
		if strings.HasSuffix(localFile, ".xlsx") {
			fileRows = external.ReadExcel(localFile, ctx)
		} else if strings.HasSuffix(localFile, ".csv") {
			fileRows = external.ReadCsv(localFile, ctx)
		}
		m.UpdateToSend(len(fileRows), campaignId)
		campaign.ToSend = len(fileRows)
		natsLogger.Info().Msgf("Rows read from campaign file %v", len(fileRows))

		for tid, tidData := range fileRows {
			var natsSubscriber models.NatsMemberPayload
			var subscriber models.Subscriber
			subscriber.Name = tid
			subscriber.Type = "Terminal"
			subscriber.UUID = strconv.Itoa(campaign.ID) + ":" + tid
			subscriber.FileAttribs = tidData
			natsSubscriber.Subscriber = subscriber
			natsSubscriber.CampaignId = strconv.Itoa(campaign.ID)
			natsSubscriber.EventId = uuid.NewString()
			natsSubscriber.EventTime = time.Now().String()
			natsSubscriber.EventType = models.RunningCampaignCron

			jsonData, err := json.Marshal(natsSubscriber)
			if err != nil {
				id := strconv.Itoa(campaignId)
				MoveCampaignToFailed(id)
				natsLogger.Error().Msgf("error marshalling json in listmonk consumer with campaign id (%d) detailed error: %v", campaignId, err)
				continue
			}

			PublishToNats([]byte(jsonData), listMonkMemberSubject, fmt.Sprintf("%s_%s_%s", natsSubscriber.CampaignId, tid, "Terminal"), ctx)
		}
		m.UpdateSentStatus(len(fileRows), campaignId)
	}
	natsLogger.Info().Msgf("processCampaign: total time taken to publish subscriber for campaignId %v = %vms", campaignId, time.Since(campaignStart).Milliseconds())
}

func startListmonkMemberConsumer() {

	lo.Info().Msgf("Started %s listmonk consumer", listMonkMemberSubject)

	for {

		msgs, _ := listmonkMemberSubscription.Fetch(1)

		for _, msg := range msgs {

			var natsPayload models.NatsMemberPayload
			msg.Ack()
			msgId := msg.Header.Get(tracer.MSG_HEADER_KEY)
			ctx := tracer.GetNatsTracingContext(msg.Header)
			natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
			natsLogger.Info().Msgf("Consumer = %s recieved messsage from nats: %s", listMonkMemberSubject, string(msg.Data))
			err := json.Unmarshal(msg.Data, &natsPayload)

			if err != nil {
				natsLogger.Error().Msgf("error while unmarshalling json with")
				continue
			}
			campId, _ := strconv.Atoi(natsPayload.CampaignId)
			c, _ := managerPointer.GetCampaignFromId(campId)

			if c == nil {
				natsLogger.Info().Msgf("campaign by id %d  is declared completed, subscriber %d rejected", campId, natsPayload.Subscriber.ID)
				continue
			}

			campMsg, err := managerPointer.NewCampaignMessage(c, natsPayload.Subscriber, ctx, msgId)
			if err != nil {
				natsLogger.Error().Msgf("error rendering message (%s) (%s): %v", c.Name, natsPayload.Subscriber.Name, err)

				utils.UpdateCounter(strconv.Itoa(c.ID)+"_"+"listmonk"+"_"+"counter_failure", 1)
				value := utils.UpdateCounter(strconv.Itoa(c.ID)+"_"+"listmonk"+"_"+"counter_total", 1)

				if value >= int64(c.ToSend) {
					managerPointer.HaltCampaign("completed", c.ID)
					managerPointer.RemoveCampaignMap(c.ID)
					managerPointer.BroadcastMessage(c.ID, "completed", ctx)
					// m.store.DeleteTargets(msg.Campaign.ID)
				}
				continue
			}

			managerPointer.PushCampaignMessage(campMsg)

		}

	}
}

func startBroadcastConsumer() {
	lo.Info().Msgf("Started %s listmonk consumer", broadcastSubject)

	for {

		msgs, _ := broadcastSubscription.Fetch(1)

		for _, msg := range msgs {

			var natsPayload models.BroadcastPayload
			err := json.Unmarshal(msg.Data, &natsPayload)
			if err != nil {
				lo.Error().Msgf("error occurred while unmarshalling json for data %v detailed error: %v", msg.Data, err)
				continue
			}
			msg.Ack()
			ctx := tracer.GetNatsTracingContext(msg.Header)
			natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
			natsLogger.Info().Msgf("Broadcast Consumer = %s recieved messsage from nats: %s", broadcastSubject, string(msg.Data))

			if natsPayload.CampaignStatus == "completed" {
				natsLogger.Info().Msgf("Received Stop signal for campaign id %s", natsPayload.CampaignId)
				id, _ := strconv.Atoi(natsPayload.CampaignId)
				managerPointer.RemoveCampaignMap(id)
			} else if natsPayload.EventName == "templateUpdated" {
				id, _ := strconv.Atoi(natsPayload.DataId)
				managerPointer.GetSingleTemplate(id)
				natsLogger.Info().Msgf("Broadcast Consumer: Updated templated with id %v in local cache", natsPayload.DataId)
			} else if natsPayload.EventName == "categoryUpdated" {
				id, _ := strconv.Atoi(natsPayload.DataId)
				managerPointer.AddCategoryMap(id)
				natsLogger.Info().Msgf("Broadcast Consumer: Updated category with id %v in local cache", natsPayload.DataId)
			} else if natsPayload.EventName == "categoryDeleted" {
				id, _ := strconv.Atoi(natsPayload.DataId)
				managerPointer.DeleteCategoryFromMap(id)
				natsLogger.Info().Msgf("Broadcast Consumer: Deleted category with id %v in local cache", natsPayload.DataId)
			} else if natsPayload.EventName == "gatewayUpdated" {
				id, _ := strconv.Atoi(natsPayload.DataId)

				if id == 0 {
					UpdateSmtpPool(natsLogger)
					natsLogger.Info().Msgf("Broadcast Consumer: updated smtp gateway in local cache")
				} else {
					managerPointer.GetSingleGateway(id, natsLogger)
					natsLogger.Info().Msgf("Broadcast Consumer: updated gateway with id %v in local cache", natsPayload.DataId)
				}

			}
		}
	}
}

func startListmonkTxnEventConsumer() {

	lo.Info().Msgf("Started %s listmonk consumer", txnEventSubject)

	for {

		msgs, _ := txnEventSubscription.Fetch(1)

		for _, msg := range msgs {

			var payload models.NatsTxnPayload
			msg.Ack()
			ctx := tracer.GetNatsTracingContext(msg.Header)
			natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

			natsLogger.Info().Msgf("TxnConsumer: Recieved message = %s", string(msg.Data))
			err := json.Unmarshal(msg.Data, &payload)

			if err != nil {
				natsLogger.Error().Msgf("TxnConsumer: error encountered while Unmarshalling transaction payload: %s", err)
				continue
			}

			if err := ProcessTxnRequest(payload.EventData, app, natsLogger, ctx); err != nil {
				natsLogger.Error().Msgf("TxnConsumer: error occured while sending the message in consumer: %v", err)
			}

		}

	}
}

type Publisher struct {
}

func (p *Publisher) NewNatsMessage(data []byte, subject string, ctx context.Context) {
	PublishToNats(data, subject, "", ctx)
}

func (p *Publisher) GetSubject() string {
	return GetBroadcastSubject()
}

func parseDuration(input string) (time.Duration, error) {
	input = strings.TrimSpace(input)
	if len(input) < 2 {
		return 0, fmt.Errorf("invalid input: %s", input)
	}

	numStr := input[:len(input)-1]
	unitStr := input[len(input)-1:]
	num, err := strconv.Atoi(numStr)
	if err != nil {
		return 0, err
	}

	unitMap := map[string]time.Duration{
		"s": time.Second,
		"m": time.Minute,
		"h": time.Hour,
		"d": 24 * time.Hour,
		"w": 7 * 24 * time.Hour,
	}

	unit, exists := unitMap[unitStr]
	if !exists {
		return 0, fmt.Errorf("invalid unit: %s", unitStr)
	}

	duration := time.Duration(num) * unit
	return duration, nil
}

func startWebhookConsumer() {
	lo.Info().Msgf("Started %s listmonk consumer", webhookSubject)

	for {

		msgs, _ := webhookSubscription.Fetch(1)

		for _, msg := range msgs {

			msg.Ack()
			var natsPayload models.NatsWebhookPayload
			err := json.Unmarshal(msg.Data, &natsPayload)
			if err != nil {
				lo.Error().Msgf("error occurred while unmarshalling json for data %v detailed error: %v", msg.Data, err)
				continue
			}
			ctx := tracer.GetNatsTracingContext(msg.Header)
			natsLogger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
			natsLogger.Info().Msgf("Webhook Consumer: recieved messsage from nats: %s", string(msg.Data))
			processWebHook(natsPayload, ctx)
			// natsLogger.Info().Msgf("hello there request_id = %v", webhookLog.RequestId)

		}
	}
}
