FROM alpine:latest
FROM golang:1.23-bookworm
ENV TZ="Asia/Kolkata"
WORKDIR /listmonk
COPY . .
RUN go install github.com/swaggo/swag/cmd/swag@latest

# Apply security hardening for CVE-2025-6297
COPY security-hardening.sh /tmp/
RUN chmod +x /tmp/security-hardening.sh && /tmp/security-hardening.sh
# Update dpkg to fix CVE-2025-6297 and install dependencies
RUN apt-get update && \
    apt-get upgrade -y dpkg && \
    apt-get install -y git curl tzdata && \
    rm -rf /var/lib/apt/lists/*

# Install latest patched FFmpeg (7.1.1+) with security fixes
RUN apt-get update && \
    apt-get install -y software-properties-common && \
    add-apt-repository ppa:savoury1/ffmpeg7 && \
    apt-get update && \
    apt-get install -y ffmpeg && \
    ffmpeg -version && \
    rm -rf /var/lib/apt/lists/*
RUN go build
# RUN swag init
EXPOSE 9000
CMD ["./listmonk"]
