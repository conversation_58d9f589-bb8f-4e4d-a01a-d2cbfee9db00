FROM alpine:latest
FROM golang:latest
ENV TZ="Asia/Kolkata"
WORKDIR /listmonk
COPY . .
RUN go install github.com/swaggo/swag/cmd/swag@latest

# Install dependencies and secure FFmpeg in one optimized layer
RUN apt-get update && \
    apt-get install -y git curl tzdata software-properties-common && \
    add-apt-repository ppa:savoury1/ffmpeg7 && \
    apt-get update && \
    apt-get install -y ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
RUN go build
# RUN swag init
EXPOSE 9000
CMD ["./listmonk"]
