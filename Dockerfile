FROM alpine:latest
FROM golang:latest
ENV TZ="Asia/Kolkata"
WORKDIR /listmonk
COPY . .
RUN go install github.com/swaggo/swag/cmd/swag@latest

RUN apt-get update && \
    apt-get install -y git curl tzdata && \
    rm -rf /var/lib/apt/lists/*

# Install latest patched FFmpeg (7.1.1+) with security fixes
RUN apt-get update && \
    apt-get install -y software-properties-common && \
    add-apt-repository ppa:savoury1/ffmpeg7 && \
    apt-get update && \
    apt-get install -y ffmpeg && \
    ffmpeg -version && \
    rm -rf /var/lib/apt/lists/*
RUN go build
# RUN swag init
EXPOSE 9000
CMD ["./listmonk"]
