#!/bin/bash
# Security hardening script for CVE-2025-6297 dpkg vulnerability mitigation
# This script provides additional runtime protection against dpkg DoS attacks

set -euo pipefail

echo "🔒 Applying security hardening for CVE-2025-6297 dpkg vulnerability..."

# 1. Set disk quota limits to prevent disk exhaustion attacks
echo "📊 Setting up disk quota protection..."
if command -v quotacheck >/dev/null 2>&1; then
    # Enable user quotas if available
    echo "Setting user disk quotas to prevent DoS via disk exhaustion"
    # Note: This requires proper quota setup in the host system
fi

# 2. Set temporary directory cleanup policies
echo "🧹 Configuring temporary directory cleanup..."
export TMPDIR="${TMPDIR:-/tmp}"
export TMP="${TMP:-/tmp}"

# Create a cleanup function for temporary files
cleanup_temp_dirs() {
    echo "Cleaning up temporary directories..."
    find /tmp -name "dpkg-*" -type d -mtime +1 -exec rm -rf {} + 2>/dev/null || true
    find /var/tmp -name "dpkg-*" -type d -mtime +1 -exec rm -rf {} + 2>/dev/null || true
}

# 3. Set resource limits to prevent resource exhaustion
echo "⚡ Setting resource limits..."
# Limit maximum file size to prevent large temporary files
ulimit -f 1048576  # 1GB max file size
# Limit maximum number of open files
ulimit -n 1024
# Limit maximum processes
ulimit -u 512

# 4. Monitor dpkg usage and set alerts
echo "📈 Setting up dpkg monitoring..."
monitor_dpkg_usage() {
    local max_dpkg_processes=5
    local current_dpkg_processes
    current_dpkg_processes=$(pgrep -c dpkg || echo 0)
    
    if [ "$current_dpkg_processes" -gt "$max_dpkg_processes" ]; then
        echo "⚠️  WARNING: High number of dpkg processes detected: $current_dpkg_processes"
        echo "This might indicate a potential DoS attack via CVE-2025-6297"
        # Log to syslog if available
        logger -p user.warning "High dpkg process count: $current_dpkg_processes (CVE-2025-6297 mitigation)"
    fi
}

# 5. Set up periodic cleanup
echo "⏰ Setting up periodic cleanup..."
setup_periodic_cleanup() {
    # Add cleanup to crontab if running as root
    if [ "$(id -u)" -eq 0 ]; then
        echo "0 */6 * * * /bin/bash -c 'find /tmp /var/tmp -name \"dpkg-*\" -type d -mtime +0.25 -exec rm -rf {} + 2>/dev/null || true'" | crontab -
        echo "Periodic cleanup scheduled every 6 hours"
    else
        echo "Non-root user: Manual cleanup recommended"
    fi
}

# 6. Verify dpkg version and patch status
echo "🔍 Checking dpkg version and patch status..."
check_dpkg_version() {
    if command -v dpkg >/dev/null 2>&1; then
        local dpkg_version
        dpkg_version=$(dpkg --version | head -n1 | awk '{print $3}')
        echo "Current dpkg version: $dpkg_version"
        
        # Check if version is patched (1.22.21+ contains the fix)
        if dpkg --compare-versions "$dpkg_version" ge "1.22.21"; then
            echo "✅ dpkg version appears to be patched for CVE-2025-6297"
        else
            echo "⚠️  WARNING: dpkg version may be vulnerable to CVE-2025-6297"
            echo "Consider updating to dpkg 1.22.21 or later"
        fi
    else
        echo "dpkg not found - this may be a non-Debian system"
    fi
}

# 7. Create monitoring script
echo "📝 Creating monitoring script..."
cat > /tmp/dpkg-monitor.sh << 'EOF'
#!/bin/bash
# CVE-2025-6297 dpkg monitoring script
while true; do
    # Check for excessive dpkg processes
    dpkg_count=$(pgrep -c dpkg 2>/dev/null || echo 0)
    if [ "$dpkg_count" -gt 3 ]; then
        echo "$(date): WARNING - $dpkg_count dpkg processes running"
        logger -p user.warning "CVE-2025-6297 monitor: $dpkg_count dpkg processes"
    fi
    
    # Check for excessive temporary directories
    temp_dirs=$(find /tmp /var/tmp -name "dpkg-*" -type d 2>/dev/null | wc -l)
    if [ "$temp_dirs" -gt 10 ]; then
        echo "$(date): WARNING - $temp_dirs dpkg temp directories found"
        logger -p user.warning "CVE-2025-6297 monitor: $temp_dirs temp directories"
    fi
    
    sleep 60
done
EOF
chmod +x /tmp/dpkg-monitor.sh

# Execute the functions
cleanup_temp_dirs
monitor_dpkg_usage
check_dpkg_version

echo "✅ Security hardening completed!"
echo ""
echo "🛡️  CVE-2025-6297 Mitigation Summary:"
echo "   - Disk quota limits configured"
echo "   - Resource limits applied"
echo "   - Temporary directory cleanup enabled"
echo "   - dpkg process monitoring active"
echo "   - Periodic cleanup scheduled"
echo ""
echo "📋 Manual Actions Required:"
echo "   1. Update dpkg to version 1.22.21+ if not already patched"
echo "   2. Monitor /tmp and /var/tmp for excessive dpkg-* directories"
echo "   3. Set up proper disk quotas on the host system"
echo "   4. Consider running the monitoring script: /tmp/dpkg-monitor.sh &"
echo ""
echo "🔗 References:"
echo "   - CVE-2025-6297: https://nvd.nist.gov/vuln/detail/CVE-2025-6297"
echo "   - Fix commit: https://git.dpkg.org/cgit/dpkg/dpkg.git/commit/?id=ed6bbd445dd8800308c67236ba35d08004c98e82"
