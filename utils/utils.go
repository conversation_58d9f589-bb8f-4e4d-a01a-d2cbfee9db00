package utils

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/security/keyvault/azkeys"
	"github.com/phuslu/log"
	push "github.com/phuslu/log"

	b64 "encoding/base64"
	"encoding/json"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/kms"
	"github.com/knadh/listmonk/models"
	"github.com/knadh/listmonk/outbound"
	"github.com/knadh/listmonk/tracer"
	"github.com/redis/go-redis/v9"
)

const (
	CONTENT_TYPE     = "Content-Type"
	APPLICATION_JSON = "application/json"
	FORM_DATA        = "application/x-www-form-urlencoded"
)

var config models.Config

func SetConfig(conf models.Config) {
	config = conf
}

func GetConfigProperties() models.Config {
	return config
}

// For kms data decryption for email
func Decryptdata(encryptInput string, logger log.Logger) string {
	if config.ServerConfig.KeyVaultUrl == "" {
		return decryptForAws(encryptInput, logger)
	} else {
		return decryptForAzure(encryptInput, logger)
	}
}

func decryptForAws(encryptInput string, logger log.Logger) string {
	sess, err := createSession()
	if err != nil {
		logger.Error().Msgf("Got error creating session: %v", err)
		return ""
	}

	svc := kms.New(sess)

	ciphertext_regular := encryptInput
	data, err1 := b64.StdEncoding.DecodeString(ciphertext_regular)

	if err1 != nil {
		logger.Error().Msgf("Got error decrypting data: %v", err1)
		return ""
	}

	inputDecrypt := &kms.DecryptInput{CiphertextBlob: data}
	respDecrypt, err := svc.Decrypt(inputDecrypt)
	if err != nil {
		logger.Error().Msgf("Got error decrypting data: %v", err)
		return ""
	}

	logger.Info().Msgf("Decrypted data: %s\n", string(respDecrypt.Plaintext))
	return string(respDecrypt.Plaintext)
}

func decryptForAzure(encryptInput string, logger log.Logger) string {
	cred, err := azidentity.NewDefaultAzureCredential(nil)
	if err != nil {
		logger.Error().Msgf("failed to obtain a credential: %v", err)
	}

	client, err := azkeys.NewClient(config.ServerConfig.KeyVaultUrl, cred, nil)
	if err != nil {
		logger.Error().Msgf("failed to create azure key vault client: %v", err)
	}

	cipherText, err := b64.StdEncoding.DecodeString(encryptInput)

	if err != nil {
		logger.Error().Msgf("Got error decoding base64string data: %v", err)
		return ""
	}

	algorithms := azkeys.PossibleEncryptionAlgorithmValues()
	opt := azkeys.KeyOperationParameters{
		Algorithm: &algorithms[13],
		Value:     cipherText,
	}
	res, err := client.Decrypt(context.Background(), "", "", opt, nil)

	if err != nil {
		logger.Error().Msgf("error while decyption : %v", err)
	}

	plainTxt := string(res.Result)

	logger.Info().Msgf("Decrypted data: %s", plainTxt)
	return plainTxt
}

func encryptForAzure(input string, logger log.Logger) string {
	cred, err := azidentity.NewDefaultAzureCredential(nil)
	if err != nil {
		logger.Error().Msgf("failed to obtain a credential: %v", err)
	}

	client, err := azkeys.NewClient(config.ServerConfig.KeyVaultUrl, cred, nil)
	if err != nil {
		logger.Error().Msgf("failed to create azure key vault client: %v", err)
	}

	algorithms := azkeys.PossibleEncryptionAlgorithmValues()
	opt := azkeys.KeyOperationParameters{
		Algorithm: &algorithms[13],
		Value:     []byte(input),
	}
	res, err := client.Encrypt(context.Background(), "", "", opt, nil)

	if err != nil {
		logger.Error().Msgf("error while decyption : %v", err)
	}

	encrypted := b64.StdEncoding.EncodeToString(res.Result)

	logger.Info().Msgf("Encrypted data: %s", encrypted)
	return encrypted
}

// For kms data encryption for email
func EncryptData(input string, logger log.Logger) string {
	if config.ServerConfig.KeyVaultUrl == "" {
		return encryptForAws(input, logger)
	} else {
		return encryptForAzure(input, logger)
	}

}

func encryptForAws(input string, logger log.Logger) string {

	sess, err := createSession()
	if err != nil {
		logger.Error().Msgf("Got error creating session: %v", err)
		return ""
	}

	svc := kms.New(sess)
	result, err := svc.Encrypt(&kms.EncryptInput{
		KeyId:     aws.String(config.ServerConfig.AesKeyId),
		Plaintext: []byte(input),
	})

	if err != nil {
		logger.Error().Msgf("Got error encrypting data: %v", err)
		return ""
	}
	return b64.StdEncoding.EncodeToString(result.CiphertextBlob)
}

// For local Redis
var localClient *redis.Client
var ctx = context.Background()

func InitalizeLocalRedis(hostname string) *redis.Client {
	localClient = redis.NewClient(&redis.Options{
		Addr:     hostname,
		Password: "",
		DB:       0,
	})
	if err := localClient.Ping(ctx).Err(); err != nil {
		panic("Unable to connect to local redis " + err.Error())
	}
	log.Info().Msg("Connected sucessfully with local redis.")
	return localClient
}

// for cluster redis

var client *redis.ClusterClient
var secondaryClient *redis.ClusterClient

// GetClient get the redis client
func InitializeClusterRedis(hostnames string, poolsize int) *redis.ClusterClient {
	log.Info().Msgf("Connecting redis server...")
	addr := strings.Split(hostnames, ",")
	client = redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:    addr,
		PoolSize: poolsize,
	})
	if err := client.Ping(ctx).Err(); err != nil {
		panic("Unable to connect to redis " + err.Error())
	}
	log.Info().Msgf("Connected sucessfully with redis cluster.")
	log.Info().Msgf("Redis Pool Size = %v and poolTimeOut =%v", client.Options().PoolSize, client.Options().PoolTimeout)
	return client
}

func InitializeSecondaryClusterRedis(hostnames string, poolsize int) *redis.ClusterClient {
	log.Info().Msgf("Connecting to secondary redis server...")
	addr := strings.Split(hostnames, ",")
	secondaryClient = redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:    addr,
		PoolSize: poolsize,
	})
	if err := secondaryClient.Ping(ctx).Err(); err != nil {
		log.Info().Msgf("Unable to connect to secondary redis " + err.Error())
		return secondaryClient
	}
	log.Info().Msgf("Connected sucessfully to secondary redis cluster.")
	log.Info().Msgf("Secondary Redis Pool Size = %v and poolTimeOut =%v", client.Options().PoolSize, client.Options().PoolTimeout)
	return secondaryClient
}

func SetSecondaryClient(primary *redis.ClusterClient) *redis.ClusterClient {
	secondaryClient = primary
	log.Info().Msgf("Secondary client is now pointing to the primary Redis cluster.")
	return secondaryClient
}

func InsertMembersIntoSet(ctx context.Context, referenceId string, targets []string, searchStrings []string, processDuration int) ([]string, []string, error) {
	var successTargets []string
	var rejectedTargets []string
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)

	if len(targets) != len(searchStrings) {
		return nil, nil, fmt.Errorf("targets and searchStrings slices must have the same length")
	}

	// Deduplicate searchStrings and map them to their respective targets
	searchStringToTarget := make(map[string]string)
	for i, searchString := range searchStrings {
		if searchString != "" {
			if _, exists := searchStringToTarget[searchString]; !exists {
				searchStringToTarget[searchString] = targets[i]
			} else {
				// If the searchString is already in the map, mark the target as rejected
				rejectedTargets = append(rejectedTargets, targets[i])
			}
		}
	}

	// Create Redis pipeline and add SAdd commands
	var pipe redis.Pipeliner
	if config.ServerConfig.IsLocal {
		pipe = localClient.Pipeline()
	} else {
		pipe = secondaryClient.Pipeline()
	}

	// Check if the Redis set exists
	existsCmd := pipe.Exists(ctx, referenceId)
	for searchString := range searchStringToTarget {
		pipe.SAdd(ctx, referenceId, searchString)
	}

	// Execute the pipeline and process results
	results, err := pipe.Exec(ctx)
	if err != nil {
		return successTargets, rejectedTargets, fmt.Errorf("error executing Redis pipeline: %v", err)
	}
	logger.Info().Msgf("Redis set exists value: %v", existsCmd.Val())
	logger.Info().Msgf("redisCampaignTTLHours is: %d", config.ServerConfig.RedisCampaignTTLHours)

	// If the key does not exist, set TTL to default value
	if existsCmd.Val() == 0 && processDuration == 0 {
		// TTL of 12 hours by default
		if config.ServerConfig.IsLocal {
			localClient.Expire(ctx, referenceId, time.Duration(config.ServerConfig.RedisCampaignTTLHours)*time.Hour)
		} else {
			secondaryClient.Expire(ctx, referenceId, time.Duration(config.ServerConfig.RedisCampaignTTLHours)*time.Hour)
		}
	} else if processDuration > 0 {
		if config.ServerConfig.IsLocal {
			localClient.Expire(ctx, referenceId, time.Duration(processDuration)*time.Minute)
		} else {
			secondaryClient.Expire(ctx, referenceId, time.Duration(processDuration)*time.Minute)
		}
	}

	// Process results to track successful and rejected targets
	for i, result := range results {
		if intCmd, ok := result.(*redis.IntCmd); ok {
			if len(intCmd.Args()) > 2 {
				searchString := intCmd.Args()[2].(string)
				target := searchStringToTarget[searchString]
				res, err := intCmd.Result()
				if err != nil {
					logger.Error().Msgf("Error retrieving result for searchString %s: %v", searchString, err)
					rejectedTargets = append(rejectedTargets, target)

				} else if res > 0 {
					successTargets = append(successTargets, target)
				} else {
					rejectedTargets = append(rejectedTargets, target)
				}
			}
		} else {
			logger.Error().Msgf("Unexpected result type for Redis command at index %d", i)
		}
	}

	logger.Info().Msgf("Success targets after Redis SAdd operations: %v", successTargets)
	logger.Info().Msgf("Final rejected targets: %v", rejectedTargets)

	return successTargets, rejectedTargets, nil
}

func EvictCampaignSet(ctx context.Context, campaignID int) error {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	redisKey := "listmonk_campaign_" + strconv.Itoa(campaignID)
	var err error
	if config.ServerConfig.IsLocal {
		_, err = localClient.Del(ctx, redisKey).Result()
	} else {
		_, err = secondaryClient.Del(ctx, redisKey).Result()
	}
	if err != nil {
		logger.Error().Msgf("Error evicting set for campaignID %v from Redis: %v", campaignID, err)
		return err
	}
	logger.Info().Msgf("Successfully evicted set for campaignID %v from Redis", campaignID)
	return nil
}

func ExecuteRedisScript(ctx context.Context, script string, keys []string, args ...interface{}) ([]int64, error) {
	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(push.Logger)
	logger.Info().Msgf("Executing Redis Lua script for keys: %s", keys)
	var pipe redis.Pipeliner
	if config.ServerConfig.IsLocal {
		pipe = localClient.Pipeline()
	} else {
		pipe = secondaryClient.Pipeline()
	}

	for i := 0; i < len(keys); i += 2 {
		pipe.EvalSha(ctx, script, []string{keys[i], keys[i+1]}, args...)
	}
	results, err := pipe.Exec(ctx)
	if err != nil {
		return nil, err
	}

	outputs := make([]int64, len(results))
	for i, cmd := range results {
		if val, err := cmd.(*redis.Cmd).Result(); err == nil {
			outputs[i] = val.(int64)
		} else {
			logger.Error().Msgf("Error executing script for key %s: %v", keys[i], err)
			outputs[i] = -1 // failed
		}
	}

	return outputs, nil
}

func GetRedisData(key string) (error, string) {
	fmt.Println("Fetching data for key:" + key)
	if config.ServerConfig.IsLocal {

		val, err := localClient.Get(ctx, key).Result()
		if err == redis.Nil || err != nil {
			return err, ""
		}
		// fmt.Println("Data for key:" + key + " is:" + val)
		return nil, val
	} else {
		val, err := client.Get(ctx, key).Result()
		if err == redis.Nil || err != nil {
			return err, ""
		}
		// fmt.Println("Data for key:" + key + " is:" + val)
		return nil, val
	}

}

func AcquireLock(lockKey string, taskName string, duration int64, logger log.Logger) bool {
	logger.Info().Msgf("Acquiring lock for schedulerName: %s", taskName)
	var val bool
	var err error
	if config.ServerConfig.IsLocal {
		val, err = localClient.SetNX(ctx, lockKey, "listmonk.scheduler.lock", time.Duration(duration)*time.Second).Result()
	} else {
		val, err = client.SetNX(ctx, lockKey, "listmonk.scheduler.lock", time.Duration(duration)*time.Second).Result()
	}
	if err != nil {
		logger.Error().Msgf("Error occured while occuring lock for %v detailed error %v", taskName, err)
		return false
	} else {
		return val
	}

}

func ReleaseLock(lockKey string, taskName string, logger log.Logger) int64 {

	logger.Info().Msgf("releasing lock for schedulerName: %s", taskName)
	var val int64
	var err error
	if config.ServerConfig.IsLocal {
		val, err = localClient.Del(ctx, lockKey).Result()
	} else {
		val, err = client.Del(ctx, lockKey).Result()
	}
	if err != nil {
		panic(err)
	} else {
		return val
	}

}

func InitializeCounter(name string, duration int64) {
	if config.ServerConfig.IsLocal {
		localClient.Set(ctx, name, 0, time.Duration(duration)*time.Second)
	} else {
		client.Set(ctx, name, 0, time.Duration(duration)*time.Second)
	}

}

func UpdateCounter(name string, by int64) int64 {
	var value int64
	if config.ServerConfig.IsLocal {
		value, _ = localClient.IncrBy(ctx, name, by).Result()
	} else {
		value, _ = client.IncrBy(ctx, name, by).Result()
	}

	return value
}

func AddedExpiration(name string, duration int64) {
	if config.ServerConfig.IsLocal {
		localClient.Expire(ctx, name, time.Duration(duration)*time.Second)
	} else {
		client.Expire(ctx, name, time.Duration(duration)*time.Second)
	}

}

func UpdateRateCounter(name string) int64 {
	var value int64
	if config.ServerConfig.IsLocal {
		value, _ = localClient.Incr(ctx, name).Result()
	} else {
		value, _ = client.Incr(ctx, name).Result()
	}
	return value
}

func createSession() (*session.Session, error) {
	var sess *session.Session
	var err error

	if config.ServerConfig.AesAccessKey == "" {
		sess, err = session.NewSession(&aws.Config{Region: &config.ServerConfig.Region})
	} else {
		creds := credentials.NewStaticCredentials(config.ServerConfig.AesAccessKey, config.ServerConfig.AesSecretKey, "")
		sess, err = session.NewSession(&aws.Config{Region: &config.ServerConfig.Region, Credentials: creds})
	}

	return sess, err
}

func HandleAuthApi(authDetails models.AuthDetails, ctx context.Context) (map[string]string, error) {

	logger := ctx.Value(tracer.TRACE_LOGGER_KEY).(log.Logger)
	messenger := authDetails.Messenger

	targetUrl := authDetails.Url
	var req *http.Request

	method := strings.ToUpper(authDetails.Method)

	if method == "GET" {
		if len(authDetails.RequestParams) != 0 {

			queryParams := url.Values{}
			for key, value := range authDetails.RequestParams {
				queryParams.Add(key, fmt.Sprintf("%v", value))
			}
			targetUrl += `?` + queryParams.Encode()
		}
		logger.Info().Msgf("%v Messenger Auth: sending request  %v", messenger, targetUrl)
		req, _ = http.NewRequest(method, targetUrl, nil)
		req.Header.Set(CONTENT_TYPE, APPLICATION_JSON)
	} else if authDetails.Type == "form" {

		data := url.Values{}
		for k, v := range authDetails.RequestBody {
			data.Set(k, fmt.Sprintf("%v", v))
		}

		logger.Info().Msgf("%v Messenger Auth: sending request %v", messenger, data)
		req, _ = http.NewRequest(method, targetUrl, strings.NewReader(data.Encode()))
		req.Header.Set(CONTENT_TYPE, FORM_DATA)
	} else {
		b, err := json.Marshal(authDetails.RequestBody)
		if err != nil {
			logger.Error().Msgf("%v Messenger Auth: error marshalling request: detailed error %v", messenger, err)
			return nil, err
		}
		logger.Info().Msgf("%v Messenger Auth: sending request %v", messenger, string(b))
		req, _ = http.NewRequest(method, targetUrl, bytes.NewBuffer(b))
		req.Header.Set(CONTENT_TYPE, APPLICATION_JSON)
	}

	if len(authDetails.Headers) != 0 {

		for key, value := range authDetails.Headers {
			req.Header.Add(key, fmt.Sprintf("%v", value))
		}
	}

	client := outbound.GetOutboundClient(config.ServerConfig.SkipSSLCheck)

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error().Msgf("%v Messenger Auth: Error reading response body: %v", messenger, err)
		return nil, err
	}
	logger.Info().Msgf("%v Messenger Auth: response from provider: %v", messenger, string(body))
	// Check the response status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected response status code: %d", resp.StatusCode)
	}
	logger.Info().Msgf("%v Messenger Auth: Successfully got response", messenger)

	if len(authDetails.Response) > 0 {
		finalResponse := make(map[string]string)
		intermediate := make(map[string]interface{})
		err := json.Unmarshal(body, &intermediate)
		if err != nil {
			logger.Error().Msgf("%v Messenger Auth: Error Unmarshalling response body: %v", messenger, err)
			return nil, err
		}
		for k, _ := range authDetails.Response {
			v, has := intermediate[k]
			if has {
				finalResponse[k] = fmt.Sprintf("%v", v)
			}
		}
		return finalResponse, nil
	}

	return map[string]string{"token": string(body)}, nil
}
