FROM redhat/ubi9:latest
FROM golang:latest
ENV TZ="Asia/Kolkata"
WORKDIR /listmonk
COPY . .

# Apply security hardening for CVE-2025-6297
COPY security-hardening.sh /tmp/
RUN chmod +x /tmp/security-hardening.sh && /tmp/security-hardening.sh

# Security: Update all packages including dpkg (CVE-2025-6297) and install dependencies
# Even though golang:latest should be recent, we ensure latest security patches
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y git curl tzdata && \
    rm -rf /var/lib/apt/lists/*

# Install latest patched FFmpeg (7.1.1+) with security fixes
RUN apt-get update && \
    apt-get install -y software-properties-common && \
    add-apt-repository ppa:savoury1/ffmpeg7 && \
    apt-get update && \
    apt-get install -y ffmpeg && \
    ffmpeg -version && \
    rm -rf /var/lib/apt/lists/*
RUN go build
EXPOSE 9000
CMD ["./listmonk"]